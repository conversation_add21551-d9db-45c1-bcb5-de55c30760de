#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <WiFiClientSecure.h>
#include <ArduinoJson.h>

const char* ssid = "tatahome";      // WiFi名称
const char* password = "m1234567";   // WiFi密码

// 和风天气API配置
const char* weatherHost = "nu33jntuuc.re.qweatherapi.com";
const char* weatherPath = "/v7/weather/now";
const char* location = "101210101";  // 杭州的城市ID
const char* key = "d48d7aaeea044c23b127a8661c74275d";    // 请替换成您的和风天气API密钥

// DNS服务器配置
IPAddress primaryDNS(8, 8, 8, 8);   // Google DNS
IPAddress secondaryDNS(1, 1, 1, 1); // Cloudflare DNS

void getWeather() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi未连接");
    return;
  }

  Serial.println("开始获取天气数据...");

  // 创建WiFiClientSecure对象
  WiFiClientSecure *client = new WiFiClientSecure;
  if (!client) {
    Serial.println("无法创建客户端");
    return;
  }

  // 跳过SSL证书验证
  client->setInsecure();

  // 创建HTTPClient对象
  HTTPClient https;

  // 构建完整的URL
  String url = "https://" + String(weatherHost) + String(weatherPath) + "?location=" + location + "&key=" + key;
  Serial.println("请求URL: " + url);

  // 配置HTTPS请求
  if (https.begin(*client, url)) {
    // 设置超时
    https.setTimeout(15000);

    // 添加请求头
    https.addHeader("User-Agent", "ESP32-Weather-Client/1.0");
    https.addHeader("Accept", "application/json");

    Serial.println("发送HTTP GET请求...");

    // 发送GET请求
    int httpCode = https.GET();
    Serial.println("HTTP响应代码: " + String(httpCode));

    if (httpCode > 0) {
      if (httpCode == HTTP_CODE_OK || httpCode == HTTP_CODE_MOVED_PERMANENTLY) {
        String payload = https.getString();
        Serial.println("API响应: " + payload);

        // 解析JSON响应
        StaticJsonDocument<1024> doc;
        DeserializationError error = deserializeJson(doc, payload);

        if (!error) {
          String code = doc["code"].as<String>();
          Serial.println("API状态码: " + code);

          if (code == "200") {
            JsonObject now = doc["now"];
            if (!now.isNull()) {
              String temp = now["temp"].as<String>();
              String text = now["text"].as<String>();
              String humidity = now["humidity"].as<String>();

              Serial.println("\n==== 杭州天气 ====");
              Serial.println("温度: " + temp + "°C");
              Serial.println("天气: " + text);
              Serial.println("湿度: " + humidity + "%");
            } else {
              Serial.println("响应中没有找到天气数据");
            }
          } else {
            Serial.println("API返回错误代码: " + code);
          }
        } else {
          Serial.println("JSON解析失败: " + String(error.c_str()));
        }
      } else {
        Serial.println("HTTP错误: " + String(httpCode));
      }
    } else {
      Serial.println("HTTP请求失败: " + https.errorToString(httpCode));
    }

    https.end();
  } else {
    Serial.println("无法连接到服务器");
  }

  delete client;
}

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("\n=== ESP32 天气监测系统启动 ===");

  // 配置DNS服务器
  WiFi.config(INADDR_NONE, INADDR_NONE, INADDR_NONE, primaryDNS, secondaryDNS);

  Serial.println("正在连接WiFi: " + String(ssid));
  WiFi.begin(ssid, password);

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("");
    Serial.println("WiFi连接成功！");
    Serial.println("IP地址: " + WiFi.localIP().toString());
    Serial.println("DNS服务器: " + WiFi.dnsIP().toString());
    Serial.println("网关: " + WiFi.gatewayIP().toString());

    // 测试DNS解析
    Serial.println("测试DNS解析...");
    IPAddress resolvedIP;
    if (WiFi.hostByName(weatherHost, resolvedIP)) {
      Serial.println("DNS解析成功: " + String(weatherHost) + " -> " + resolvedIP.toString());
    } else {
      Serial.println("DNS解析失败: " + String(weatherHost));
    }
  } else {
    Serial.println("");
    Serial.println("WiFi连接失败！");
  }
}

void loop() {
  // 检查WiFi连接状态
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi连接断开，正在重新连接...");
    WiFi.disconnect();
    delay(1000);
    WiFi.begin(ssid, password);

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
      delay(500);
      Serial.print(".");
      attempts++;
    }

    if (WiFi.status() == WL_CONNECTED) {
      Serial.println("");
      Serial.println("WiFi重新连接成功！");
      Serial.println("IP地址: " + WiFi.localIP().toString());
    } else {
      Serial.println("");
      Serial.println("WiFi重新连接失败，等待下次尝试...");
      delay(30000); // 等待30秒后重试
      return;
    }
  }

  // 获取天气数据
  Serial.println("\n--- 开始新的天气查询 ---");
  getWeather();

  Serial.println("等待5分钟后进行下次查询...");
  delay(300000); // 每5分钟更新一次天气
}
