#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <WiFiClientSecure.h>
#include <ArduinoJson.h>

const char* ssid = "tatahome";      // WiFi名称
const char* password = "m1234567";   // WiFi密码

// 和风天气API配置
const char* weatherHost = "devapi.qweather.com";
const char* weatherPath = "/v7/weather/now";
const char* location = "101210101";  // 杭州的城市ID
const char* key = "d48d7aaeea044c23b127a8661c74275d";    // 请替换成您的和风天气API密钥

// 和风天气的SSL证书指纹
WiFiClientSecure client;

void getWeather() {
  if (WiFi.status() == WL_CONNECTED) {
    client.setInsecure(); // 跳过证书验证，仅用于测试。生产环境建议使用证书验证
    HTTPClient http;
    String url = "https://" + String(weatherHost) + String(weatherPath) + "?location=" + location + "&key=" + key;
    Serial.println("请求URL: " + url);

    http.begin(client, url);
    http.setTimeout(10000); // 设置10秒超时
    int httpCode = http.GET();
    Serial.println("HTTP响应代码: " + String(httpCode));

    if (httpCode > 0) {
      String payload = http.getString();
      
      Serial.println(payload);
    
      // 使用ArduinoJson解析返回的JSON数据
      StaticJsonDocument<1024> doc;
      DeserializationError error = deserializeJson(doc, payload);
      
      if (!error) {
        // 检查API响应状态
        String code = doc["code"].as<String>();
        if (code == "200") {
          JsonObject now = doc["now"];
          String temp = now["temp"].as<String>();
          String text = now["text"].as<String>();
          String humidity = now["humidity"].as<String>();

          Serial.println("\n==== 杭州天气 ====");
          Serial.print("温度: ");
          Serial.print(temp);
          Serial.println("°C");
          Serial.print("天气: ");
          Serial.println(text);
          Serial.print("湿度: ");
          Serial.print(humidity);
          Serial.println("%");
        } else {
          Serial.println("API错误代码: " + code);
        }
      } else {
        Serial.println("JSON解析失败: " + String(error.c_str()));
      }
    } else {
      Serial.println("HTTP请求失败，错误代码: " + String(httpCode));
      Serial.println("可能的原因: 网络连接问题或API服务器不可达");
    }
    
    http.end();
  }
}

void setup() {
  Serial.begin(115200);
  Serial.println("正在连接WiFi...");
  
  WiFi.begin(ssid, password);
  
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  
  Serial.println("");
  Serial.println("WiFi连接成功！");
  Serial.print("IP地址: ");
  Serial.println(WiFi.localIP());
}

void loop() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi连接断开，正在重新连接...");
    WiFi.begin(ssid, password);
    
    while (WiFi.status() != WL_CONNECTED) {
      delay(500);
      Serial.print(".");
    }
    
    Serial.println("");
    Serial.println("WiFi重新连接成功！");
  }
  
  getWeather();  // 获取天气数据
  delay(300000); // 每5分钟更新一次天气
}
