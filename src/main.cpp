#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <WiFiClientSecure.h>
#include <ArduinoJson.h>

const char* ssid = "tatahome";      // WiFi名称
const char* password = "m1234567";   // WiFi密码

// 和风天气API配置
const char* weatherHost = "nu33jntuuc.re.qweatherapi.com";
const char* weatherPath = "/v7/weather/now";
const char* location = "101210101";  // 杭州的城市ID
const char* key = "d48d7aaeea044c23b127a8661c74275d";    // 请替换成您的和风天气API密钥

// DNS服务器配置
IPAddress primaryDNS(8, 8, 8, 8);   // Google DNS
IPAddress secondaryDNS(1, 1, 1, 1); // Cloudflare DNS

// 处理天气数据的函数
bool processWeatherData(String payload) {
  Serial.println("开始解析JSON...");
  Serial.println("JSON数据长度: " + String(payload.length()));
  
  // 检查数据是否为有效JSON格式
  if (payload.length() == 0 || payload.charAt(0) != '{') {
    Serial.println("无效的JSON数据格式");
    return false;
  }
  
  // 使用ArduinoJson解析返回的JSON数据
  StaticJsonDocument<1024> doc;
  DeserializationError error = deserializeJson(doc, payload);
  Serial.println("JSON解析结果: " + String(error.c_str()));
  
  if (!error) {
    String code = doc["code"].as<String>();
    Serial.println("API状态码: " + code);
    
    if (code == "200") {
      JsonObject now = doc["now"];
      if (!now.isNull()) {
        String temp = now["temp"].as<String>();
        String text = now["text"].as<String>();
        String humidity = now["humidity"].as<String>();
        
        Serial.println("\n==== 杭州天气 ====");
        Serial.println("温度: " + temp + "°C");
        Serial.println("天气: " + text);
        Serial.println("湿度: " + humidity + "%");
        return true;
      } else {
        Serial.println("响应中没有找到天气数据");
      }
    } else {
      Serial.println("API返回错误代码: " + code);
    }
  } else {
    Serial.println("JSON解析失败: " + String(error.c_str()));
  }
  return false;
}

void getWeather() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi未连接");
    return;
  }

  Serial.println("开始获取天气数据...");
  
  // 构建完整的URL
  String url = "https://" + String(weatherHost) + String(weatherPath) + "?location=" + location + "&key=" + key;
  Serial.println("请求URL: " + url);
  
  // 创建WiFi客户端
  WiFiClient client;
  HTTPClient http;
  
  // 配置HTTP请求
  if (http.begin(client, url)) {
    // 设置超时
    http.setTimeout(15000);
    
    // 添加请求头
    http.addHeader("User-Agent", "ESP32-Weather-Client/1.0");
    http.addHeader("Accept", "application/json");
    http.addHeader("Accept-Encoding", "identity"); // 禁用压缩
    http.addHeader("Connection", "close");
    
    Serial.println("发送HTTP GET请求...");
    
    // 发送GET请求
    int httpCode = http.GET();
    Serial.println("HTTP响应代码: " + String(httpCode));
    
    if (httpCode > 0) {
      if (httpCode == HTTP_CODE_OK) {
        String payload = http.getString();
        Serial.println("API响应长度: " + String(payload.length()));
        
        // 显示响应的前200个字符用于调试
        int displayLength = min(200, (int)payload.length());
        Serial.println("API响应前" + String(displayLength) + "字符:");
        Serial.println(payload.substring(0, displayLength));
        
        // 处理天气数据
        processWeatherData(payload);
        
      } else {
        Serial.println("HTTP错误: " + String(httpCode));
      }
    } else {
      Serial.println("HTTP请求失败: " + http.errorToString(httpCode));
    }
    
    http.end();
  } else {
    Serial.println("无法连接到服务器");
  }
}

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("\n=== ESP32 天气监测系统启动 ===");
  
  // 配置DNS服务器
  WiFi.config(INADDR_NONE, INADDR_NONE, INADDR_NONE, primaryDNS, secondaryDNS);
  
  Serial.println("正在连接WiFi: " + String(ssid));
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("");
    Serial.println("WiFi连接成功！");
    Serial.println("IP地址: " + WiFi.localIP().toString());
    Serial.println("DNS服务器: " + WiFi.dnsIP().toString());
    Serial.println("网关: " + WiFi.gatewayIP().toString());
    
    // 测试DNS解析
    Serial.println("测试DNS解析...");
    IPAddress resolvedIP;
    if (WiFi.hostByName(weatherHost, resolvedIP)) {
      Serial.println("DNS解析成功: " + String(weatherHost) + " -> " + resolvedIP.toString());
    } else {
      Serial.println("DNS解析失败: " + String(weatherHost));
    }
  } else {
    Serial.println("");
    Serial.println("WiFi连接失败！");
  }
}

void loop() {
  // 检查WiFi连接状态
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi连接断开，正在重新连接...");
    WiFi.disconnect();
    delay(1000);
    WiFi.begin(ssid, password);
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
      delay(500);
      Serial.print(".");
      attempts++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
      Serial.println("");
      Serial.println("WiFi重新连接成功！");
      Serial.println("IP地址: " + WiFi.localIP().toString());
    } else {
      Serial.println("");
      Serial.println("WiFi重新连接失败，等待下次尝试...");
      delay(30000); // 等待30秒后重试
      return;
    }
  }
  
  // 获取天气数据
  Serial.println("\n--- 开始新的天气查询 ---");
  getWeather();
  
  Serial.println("等待5s后进行下次查询...");
  delay(5000); // 每5s更新一次天气
}
