; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:featheresp32]
platform = espressif32
board = featheresp32
framework = arduino
upload_speed = 115200
upload_flags =
    --before=default_reset
    --after=hard_reset
    --connect-attempts=3
lib_deps =
    bblanchon/ArduinoJson @ ^6.21.3
